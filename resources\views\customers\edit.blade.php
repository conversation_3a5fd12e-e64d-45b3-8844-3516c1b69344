@extends('layouts.index')

@section('content')
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card card-primary">
                <div class="card-header">
                    <h3 class="card-title text-center fw-bold py-2">Müşteri Düzenle</h3>
                </div>
                <div class="card-body">
                    <form action="{{ route('customers.update', $customer->id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <!-- <PERSON>lik Bilgileri -->
                        <h5 class="mb-3 text-primary"><PERSON>lik Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="tc" class="form-label">TC Kimlik No</label>
                                    <input type="text" class="form-control" id="tc" name="tc" value="{{ old('tc', $customer->tc) }}" maxlength="11" minlength="11" pattern="\d{11}" inputmode="numeric" placeholder="11 haneli TC Kimlik No">
                                    <small class="form-text text-muted">11 haneli TC Kimlik Numarası giriniz.</small>
                                    @error('tc')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="pluscard_no" class="form-label">Pluscard No</label>
                                    <input type="text" class="form-control" id="pluscard_no" name="pluscard_no" value="{{ old('pluscard_no', $customer->pluscard_no) }}" placeholder="Pluscard numarası">
                                    @error('pluscard_no')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label">E-posta <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ old('email', $customer->email) }}" required placeholder="<EMAIL>">
                                    @error('email')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Firma Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Firma Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="company_name" class="form-label">Firma Adı <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" value="{{ old('company_name', $customer->company_name) }}" required placeholder="Firma adı">
                                    @error('company_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="website_url" class="form-label">Web Sitesi</label>
                                    <input type="url" class="form-control" id="website_url" name="website_url" value="{{ old('website_url', $customer->website_url) }}" placeholder="https://example.com">
                                    @error('website_url')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="google_maps_url" class="form-label">Google Harita Linki</label>
                                    <input type="url" class="form-control" id="google_maps_url" name="google_maps_url" value="{{ old('google_maps_url', $customer->google_maps_url) }}" placeholder="https://maps.google.com/...">
                                    @error('google_maps_url')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="listing_url" class="form-label">İlan Sitesi Linki</label>
                                    <input type="url" class="form-control" id="listing_url" name="listing_url" value="{{ old('listing_url', $customer->listing_url) }}" placeholder="https://ilan.example.com/...">
                                    @error('listing_url')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="dealer_id" class="form-label">Bayi</label>
                                    <select class="form-control" id="dealer_id" name="dealer_id">
                                        <option value="">Bayi Seçin</option>
                                        @foreach($dealers as $dealer)
                                            <option value="{{ $dealer->id }}" {{ old('dealer_id', $customer->dealer_id) == $dealer->id ? 'selected' : '' }}>
                                                {{ $dealer->name }} ({{ $dealer->region->name ?? 'Bölge Yok' }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('dealer_id')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Yetkili Kişi Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Yetkili Kişi Bilgileri</h5>

                        <!-- Eski tek yetkili kişi alanları (geriye dönük uyumluluk için) -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_title" class="form-label">Yetkili Ünvan</label>
                                    <input type="text" class="form-control" id="authorized_title" name="authorized_title" value="{{ old('authorized_title', $customer->authorized_title) }}" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                    @error('authorized_title')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_phone" class="form-label">Yetkili Telefon</label>
                                    <input type="text" class="form-control inputmask" id="authorized_phone" name="authorized_phone" value="{{ old('authorized_phone', $customer->authorized_phone) }}" data-inputmask="'mask': '+99 999 999 99 99'" inputmode="tel" pattern="\+[0-9 ]{13,16}" placeholder="+90 5xx xxx xx xx">
                                    @error('authorized_phone')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_first_name" class="form-label">Yetkili İsim</label>
                                    <input type="text" class="form-control" id="authorized_first_name" name="authorized_first_name" value="{{ old('authorized_first_name', $customer->authorized_first_name) }}" placeholder="Ad">
                                    @error('authorized_first_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="authorized_last_name" class="form-label">Yetkili Soyisim</label>
                                    <input type="text" class="form-control" id="authorized_last_name" name="authorized_last_name" value="{{ old('authorized_last_name', $customer->authorized_last_name) }}" placeholder="Soyad">
                                    @error('authorized_last_name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Yeni çoklu yetkili kişi sistemi -->
                        <h6 class="mb-3 text-secondary mt-4">Ek Yetkili Kişiler</h6>
                        <div class="mb-3">
                            <button type="button" id="add-authorized-person" class="btn btn-sm btn-success">
                                <i class="fas fa-plus"></i> Yeni Yetkili Ekle
                            </button>
                        </div>

                        <div id="authorized-persons-container">
                            @if($customer->authorizedPersons && $customer->authorizedPersons->count() > 0)
                                @foreach($customer->authorizedPersons as $index => $person)
                                    <div class="authorized-person-item border p-3 mb-3 rounded" data-index="{{ $index }}">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Yetkili Kişi #{{ $index + 1 }}</h6>
                                            <button type="button" class="btn btn-sm btn-danger remove-authorized-person">
                                                <i class="fas fa-trash"></i> Kaldır
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Ünvan</label>
                                                    <input type="text" class="form-control" name="authorized_persons[{{ $index }}][title]" value="{{ old('authorized_persons.'.$index.'.title', $person->title) }}" placeholder="Örn: Genel Müdür, Satış Müdürü">
                                                    @error('authorized_persons.'.$index.'.title')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Telefon</label>
                                                    <input type="text" class="form-control inputmask" name="authorized_persons[{{ $index }}][phone]" value="{{ old('authorized_persons.'.$index.'.phone', $person->phone) }}" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                                                    @error('authorized_persons.'.$index.'.phone')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili İsim</label>
                                                    <input type="text" class="form-control" name="authorized_persons[{{ $index }}][first_name]" value="{{ old('authorized_persons.'.$index.'.first_name', $person->first_name) }}" placeholder="Ad">
                                                    @error('authorized_persons.'.$index.'.first_name')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Yetkili Soyisim</label>
                                                    <input type="text" class="form-control" name="authorized_persons[{{ $index }}][last_name]" value="{{ old('authorized_persons.'.$index.'.last_name', $person->last_name) }}" placeholder="Soyad">
                                                    @error('authorized_persons.'.$index.'.last_name')<div class="text-danger">{{ $message }}</div>@enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>

                        <!-- İletişim Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">İletişim Bilgileri</h5>

                        <!-- Şirket Telefonları -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">Şirket Telefonları</h6>
                                <button type="button" class="btn btn-sm btn-primary" id="add-company-phone">
                                    <i class="fas fa-plus"></i> Telefon Ekle
                                </button>
                            </div>

                            <div id="company-phones-container">
                                @php
                                    $allPhones = collect();

                                    if(method_exists($customer, 'phones') && $customer->phones) {
                                        $allPhones = $customer->phones;
                                    }

                                    $phoneIndex = 0;
                                @endphp

                                @if($allPhones->count() > 0)
                                    @foreach($allPhones as $phone)
                                        <div class="company-phone-item border p-3 mb-3 rounded" data-index="{{ $phoneIndex }}">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Şirket Telefon #{{ $phoneIndex + 1 }}</h6>
                                                <button type="button" class="btn btn-sm btn-danger remove-company-phone" {{ $phoneIndex == 0 && $allPhones->count() == 1 ? 'style=display:none;' : '' }}>
                                                    <i class="fas fa-trash"></i> Kaldır
                                                </button>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label class="form-label">Telefon Numarası</label>
                                                        <input type="text" class="form-control inputmask" name="company_phones[{{ $phoneIndex }}][phone]" value="{{ old('company_phones.'.$phoneIndex.'.phone', $phone->phone) }}" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                                        @if($phone->id)
                                                            <input type="hidden" name="company_phones[{{ $phoneIndex }}][id]" value="{{ $phone->id }}">
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label class="form-label">Telefon Türü</label>
                                                        <select class="form-control" name="company_phones[{{ $phoneIndex }}][type]">
                                                            <option value="Sabit" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Sabit' ? 'selected' : '' }}>Sabit Hat</option>
                                                            <option value="Mobil" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Mobil' ? 'selected' : '' }}>Mobil</option>
                                                            <option value="Fax" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Fax' ? 'selected' : '' }}>Fax</option>
                                                            <option value="Diğer" {{ old('company_phones.'.$phoneIndex.'.type', $phone->type) == 'Diğer' ? 'selected' : '' }}>Diğer</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @php $phoneIndex++; @endphp
                                    @endforeach
                                @else
                                    <!-- Hiç telefon yoksa varsayılan bir alan göster -->
                                    <div class="company-phone-item border p-3 mb-3 rounded" data-index="0">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">Şirket Telefon #1</h6>
                                            <button type="button" class="btn btn-sm btn-danger remove-company-phone" style="display: none;">
                                                <i class="fas fa-trash"></i> Kaldır
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Telefon Numarası</label>
                                                    <input type="text" class="form-control inputmask" name="company_phones[0][phone]" value="{{ old('company_phones.0.phone', $customer->phone_1) }}" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label class="form-label">Telefon Türü</label>
                                                    <select class="form-control" name="company_phones[0][type]">
                                                        <option value="Sabit" {{ old('company_phones.0.type', 'Sabit') == 'Sabit' ? 'selected' : '' }}>Sabit Hat</option>
                                                        <option value="Mobil" {{ old('company_phones.0.type') == 'Mobil' ? 'selected' : '' }}>Mobil</option>
                                                        <option value="Fax" {{ old('company_phones.0.type') == 'Fax' ? 'selected' : '' }}>Fax</option>
                                                        <option value="Diğer" {{ old('company_phones.0.type') == 'Diğer' ? 'selected' : '' }}>Diğer</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @php $phoneIndex = 1; @endphp
                                @endif
                            </div>
                        </div>

                        <!-- Adres Bilgileri -->
                        <h5 class="mb-3 text-primary mt-4">Adres Bilgileri</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label">İl <span class="text-danger">*</span></label>
                                    <select class="form-control" id="city" name="city" required>
                                        <option value="">İl seçiniz</option>
                                    </select>
                                    @error('city')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="district" class="form-label">İlçe <span class="text-danger">*</span></label>
                                    <select class="form-control" id="district" name="district" required disabled>
                                        <option value="">İlçe seçiniz</option>
                                    </select>
                                    @error('district')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="dealer_id" class="form-label">Bayi</label>
                                    <select class="form-control @error('dealer_id') is-invalid @enderror" id="dealer_id" name="dealer_id">
                                        <option value="">Bayi Seçiniz</option>
                                        @foreach(\App\Models\Dealer::active()->with('region')->orderBy('name')->get() as $dealer)
                                            <option value="{{ $dealer->id }}" {{ old('dealer_id', $customer->dealer_id) == $dealer->id ? 'selected' : '' }}>
                                                {{ $dealer->name }} ({{ $dealer->region->name ?? '' }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('dealer_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Adres <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="address" name="address" rows="3" required placeholder="Detaylı adres bilgisi">{{ old('address', $customer->address) }}</textarea>
                                    @error('address')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="card-footer d-flex flex-column gap-2">
                            <button type="submit" class="btn btn-success w-100">Güncelle</button>
                            <a href="{{ route('customers.index') }}" class="btn btn-secondary w-100">İptal</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
    <script src="/assets/plugins/inputmask/jquery.inputmask.bundle.js"></script>
    <script>
        // TC Kimlik No sadece rakam ve 11 hane
        document.addEventListener('DOMContentLoaded', function() {
            var tcInput = document.getElementById('tc');
            if(tcInput) {
                tcInput.addEventListener('input', function (e) {
                    this.value = this.value.replace(/[^0-9]/g, '').slice(0, 11);
                });
            }
        });
        let cityDistricts = {};
        $(document).ready(function(){
            const citySelect = $('#city');
            const districtSelect = $('#district');
            fetch('/assets/turkiye-il-ilce.json')
                .then(response => response.json())
                .then(data => {
                    cityDistricts = data;
                    Object.keys(cityDistricts).forEach(function(city) {
                        citySelect.append(`<option value="${city}">${city}</option>`);
                    });
                    // Eski değerleri doldur
                    const oldCity = "{{ old('city', $customer->city) }}";
                    const oldDistrict = "{{ old('district', $customer->district) }}";
                    if (oldCity) {
                        citySelect.val(oldCity).trigger('change');
                        if (oldDistrict) {
                            setTimeout(function() {
                                districtSelect.val(oldDistrict);
                            }, 100);
                        }
                    }
                });
            citySelect.on('change', function() {
                const selectedCity = $(this).val();
                districtSelect.empty().append('<option value="">İlçe seçiniz</option>');
                if (selectedCity && cityDistricts[selectedCity]) {
                    cityDistricts[selectedCity].forEach(function(district) {
                        districtSelect.append(`<option value="${district}">${district}</option>`);
                    });
                    districtSelect.prop('disabled', false);
                } else {
                    districtSelect.prop('disabled', true);
                }
            });
            $(".inputmask").inputmask({
                mask: "+99 999 999 99 99",
                showMaskOnHover: false,
                showMaskOnFocus: true,
                clearIncomplete: true,
                definitions: {
                    '9': {
                        validator: "[0-9]",
                        cardinality: 1,
                        definitionSymbol: "9"
                    }
                },
                onBeforePaste: function (pastedValue, opts) {
                    return pastedValue.replace(/[^\d\+]/g, '');
                },
                onKeyDown: function(e, buffer, caretPos, opts) {
                    var key = e.key;
                    if (!/[0-9]/.test(key) && key.length === 1) {
                        e.preventDefault();
                    }
                }
            });

    // Yetkili kişi yönetimi
    let authorizedPersonIndex = {{ $customer->authorizedPersons ? $customer->authorizedPersons->count() : 0 }};
    let companyPhoneIndex = {{ $allPhones->count() > 0 ? $allPhones->count() : 1 }};

    function updateRemoveButtons() {
        const items = $('.authorized-person-item');
        if (items.length <= 1) {
            $('.remove-authorized-person').hide();
        } else {
            $('.remove-authorized-person').show();
        }
    }

    function updatePersonNumbers() {
        $('.authorized-person-item').each(function(index) {
            $(this).find('h6').text('Yetkili Kişi #' + (index + 1));
        });
    }

    function updatePhoneRemoveButtons() {
        const items = $('.company-phone-item');
        if (items.length <= 1) {
            $('.remove-company-phone').hide();
        } else {
            $('.remove-company-phone').show();
        }
    }

    function updatePhoneNumbers() {
        $('.company-phone-item').each(function(index) {
            $(this).find('h6').text('Şirket Telefon #' + (index + 1));
            $(this).attr('data-index', index);

            // Input name'lerini güncelle
            $(this).find('input[name*="[phone]"]').attr('name', 'company_phones[' + index + '][phone]');
            $(this).find('select[name*="[type]"]').attr('name', 'company_phones[' + index + '][type]');
            $(this).find('input[name*="[id]"]').attr('name', 'company_phones[' + index + '][id]');
        });
    }

    $('#add-authorized-person').on('click', function() {
        const container = $('#authorized-persons-container');
        const newItem = `
            <div class="authorized-person-item border p-3 mb-3 rounded" data-index="${authorizedPersonIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Yetkili Kişi #${authorizedPersonIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-authorized-person">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Ünvan</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][title]" placeholder="Örn: Genel Müdür, Satış Müdürü">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Telefon</label>
                            <input type="text" class="form-control inputmask" name="authorized_persons[${authorizedPersonIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 5xx xxx xx xx">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili İsim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][first_name]" placeholder="Ad">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Yetkili Soyisim</label>
                            <input type="text" class="form-control" name="authorized_persons[${authorizedPersonIndex}][last_name]" placeholder="Soyad">
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.append(newItem);

        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });

        authorizedPersonIndex++;
        updateRemoveButtons();
    });

    $(document).on('click', '.remove-authorized-person', function() {
        $(this).closest('.authorized-person-item').remove();
        updateRemoveButtons();
        updatePersonNumbers();
    });

    // Şirket telefonu ekleme
    $('#add-company-phone').on('click', function() {
        const container = $('#company-phones-container');
        const newItem = `
            <div class="company-phone-item border p-3 mb-3 rounded" data-index="${companyPhoneIndex}">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Şirket Telefon #${companyPhoneIndex + 1}</h6>
                    <button type="button" class="btn btn-sm btn-danger remove-company-phone">
                        <i class="fas fa-trash"></i> Kaldır
                    </button>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Telefon Numarası</label>
                            <input type="text" class="form-control inputmask" name="company_phones[${companyPhoneIndex}][phone]" data-inputmask="'mask': '+99 999 999 99 99'" placeholder="+90 2xx xxx xx xx">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label class="form-label">Telefon Türü</label>
                            <select class="form-control" name="company_phones[${companyPhoneIndex}][type]">
                                <option value="Sabit">Sabit Hat</option>
                                <option value="Mobil">Mobil</option>
                                <option value="Fax">Fax</option>
                                <option value="Diğer">Diğer</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.append(newItem);

        // Yeni eklenen telefon alanına inputmask uygula
        container.find('.inputmask').last().inputmask({
            mask: "+99 999 999 99 99",
            showMaskOnHover: false,
            showMaskOnFocus: true,
            clearIncomplete: true
        });

        companyPhoneIndex++;
        updatePhoneRemoveButtons();
    });

    // Şirket telefonu kaldırma
    $(document).on('click', '.remove-company-phone', function() {
        $(this).closest('.company-phone-item').remove();
        updatePhoneRemoveButtons();
        updatePhoneNumbers();
    });

    // Sayfa yüklendiğinde remove butonlarını güncelle
    updateRemoveButtons();
    updatePhoneRemoveButtons();

        });
    </script>
@endpush